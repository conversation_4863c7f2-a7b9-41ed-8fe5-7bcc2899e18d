import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyTokenInMiddleware(token: string): boolean {
  try {
    jwt.verify(token, JWT_SECRET);
    return true;
  } catch (error) {
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const authPaths = ["/login", "/register", "/forgot-password"];
  const protectedPaths = ["/home", "/dashboard", "/profile", "/settings"];
  const publicPaths = ["/landing", "/debug"];

  console.log(`🛡️ Middleware: ${request.method} ${path}`);

  // Skip middleware for API routes
  if (path.startsWith("/api")) {
    console.log("⏭️ Skipping API route");
    return NextResponse.next();
  }

  const cookie = request.cookies.get("auth_token")?.value;
  console.log(`🍪 Auth cookie present: ${!!cookie}`);

  // Allow public paths for everyone
  if (publicPaths.includes(path)) {
    console.log("🌍 Public path, allowing access");
    return NextResponse.next();
  }

  // Redirect authenticated users away from auth pages
  if (authPaths.includes(path)) {
    if (cookie && verifyTokenInMiddleware(cookie)) {
      console.log(
        "🔄 Authenticated user on auth page, redirecting to dashboard"
      );
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
    console.log("👤 Unauthenticated user on auth page, allowing access");
    return NextResponse.next();
  }

  // Protect dashboard and other protected routes
  if (protectedPaths.some((p) => path.startsWith(p))) {
    if (!cookie || !verifyTokenInMiddleware(cookie)) {
      console.log(
        "🚫 Unauthenticated user on protected route, redirecting to login"
      );
      return NextResponse.redirect(new URL("/login", request.url));
    }
    console.log("✅ Authenticated user on protected route, allowing access");
    return NextResponse.next();
  }

  console.log("⏭️ Path not matched, allowing access");
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
