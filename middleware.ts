import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyTokenInMiddleware(token: string): boolean {
  try {
    jwt.verify(token, JWT_SECRET);
    return true;
  } catch (error) {
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const authPaths = ["/login", "/register", "/forgot-password"];
  const protectedPaths = ["/home", "/dashboard", "/profile", "/settings"];
  const publicPaths = ["/landing"];

  // Skip middleware for API routes
  if (path.startsWith("/api")) {
    return NextResponse.next();
  }

  const cookie = request.cookies.get("auth_token")?.value;

  // Allow public paths for everyone
  if (publicPaths.includes(path)) {
    return NextResponse.next();
  }

  // Redirect authenticated users away from auth pages
  if (authPaths.includes(path)) {
    if (cookie && verifyTokenInMiddleware(cookie)) {
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
    return NextResponse.next();
  }

  // Protect dashboard and other protected routes
  if (protectedPaths.some((p) => path.startsWith(p))) {
    if (!cookie || !verifyTokenInMiddleware(cookie)) {
      return NextResponse.redirect(new URL("/login", request.url));
    }
    return NextResponse.next();
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
