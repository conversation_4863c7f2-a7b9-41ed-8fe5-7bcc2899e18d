import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { verifyToken } from "./lib/auth";

export async function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;
  const authPaths = ["/login", "/register"];
  const protectedPaths = ["/dashboard"];

  // Skip middleware for API routes
  if (path.startsWith("/api")) {
    return NextResponse.next();
  }

  const cookie = request.cookies.get("auth_token")?.value;

  // Redirect authenticated users away from auth pages
  if (authPaths.includes(path)) {
    if (cookie) {
      try {
        verifyToken(cookie);
        return NextResponse.redirect(new URL("/dashboard", request.url));
      } catch (error) {
        // Token is invalid, proceed to auth page
        return NextResponse.next();
      }
    }
    return NextResponse.next();
  }

  // Protect dashboard and other protected routes
  if (protectedPaths.some((p) => path.startsWith(p))) {
    if (!cookie) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    try {
      verifyToken(cookie);
      return NextResponse.next();
    } catch (error) {
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
