'use server';

import { generateToken, setAuth<PERSON><PERSON>ie, clearAuth<PERSON><PERSON>ie } from './auth';
import { User, IUser } from './models/User';
import dbConnect from './db';
import { LoginCredentials, RegisterCredentials } from './types';

export async function registerUser(
  credentials: RegisterCredentials
): Promise<{ success: boolean; message?: string; user?: IUser }> {
  try {
    const { email, password, confirmPassword } = credentials;

    if (!email || !password || !confirmPassword) {
      return { success: false, message: 'Missing fields' };
    }

    if (password !== confirmPassword) {
      return { success: false, message: 'Passwords do not match' };
    }

    if (password.length < 8) {
      return { success: false, message: 'Password must be at least 8 characters' };
    }

    await dbConnect();

    // Check if user exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return { success: false, message: 'User already exists' };
    }

    // Create new user
    const newUser = new User({
      email,
      passwordHash: password, // Will be hashed by pre-save hook
    });

    const savedUser = await newUser.save();
    const user = savedUser.toObject();

    // Generate token and set cookie
    const token = generateToken(user);
    await setAuthCookie(token);

    return {
      success: true,
      user: {
        _id: user._id.toString(),
        email: user.email,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      } as IUser,
    };
  } catch (error: any) {
    console.error('Registration error:', error);
    return { success: false, message: error.message || 'Internal server error' };
  }
}

export async function loginUser(
  credentials: LoginCredentials
): Promise<{ success: boolean; message?: string; user?: IUser }> {
  try {
    const { email, password } = credentials;

    if (!email || !password) {
      return { success: false, message: 'Missing credentials' };
    }

    await dbConnect();

    const user = await User.findOne({ email });
    if (!user) {
      return { success: false, message: 'Invalid credentials' };
    }

    const isValid = await user.comparePassword(password);
    if (!isValid) {
      return { success: false, message: 'Invalid credentials' };
    }

    const token = generateToken(user);
    await setAuthCookie(token);

    return {
      success: true,
      user: {
        _id: user._id.toString(),
        email: user.email,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      } as IUser,
    };
  } catch (error: any) {
    console.error('Login error:', error);
    return { success: false, message: error.message || 'Internal server error' };
  }
}

export async function logoutUser(): Promise<{ success: boolean; message?: string }> {
  try {
    await clearAuthCookie();
    return { success: true };
  } catch (error: any) {
    console.error('Logout error:', error);
    return { success: false, message: error.message || 'Internal server error' };
  }
}
