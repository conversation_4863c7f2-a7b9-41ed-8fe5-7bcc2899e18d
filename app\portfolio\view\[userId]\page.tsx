import { notFound } from 'next/navigation';
import dbConnect from '../../../../lib/db';
import { PortfolioProfile, Project, Skill, Experience } from '../../../../lib/models/Portfolio';

interface PageProps {
  params: {
    userId: string;
  };
}

async function getPortfolioData(userId: string) {
  try {
    await dbConnect();

    const profile = await PortfolioProfile.findOne({ userId, isPublic: true }).lean();
    if (!profile) {
      return null;
    }

    const [projects, skills, experiences] = await Promise.all([
      Project.find({ userId }).sort({ featured: -1, order: 1, createdAt: -1 }).lean(),
      Skill.find({ userId }).sort({ category: 1, order: 1 }).lean(),
      Experience.find({ userId }).sort({ startDate: -1 }).lean(),
    ]);

    return {
      profile,
      projects,
      skills,
      experiences,
    };
  } catch (error) {
    console.error('Error fetching portfolio data:', error);
    return null;
  }
}

export default async function PublicPortfolioPage({ params }: PageProps) {
  const portfolioData = await getPortfolioData(params.userId);

  if (!portfolioData) {
    notFound();
  }

  const { profile, projects, skills, experiences } = portfolioData;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">{profile.name}</h1>
            <div className="flex space-x-4">
              {profile.linkedin && (
                <a
                  href={profile.linkedin}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-blue-600"
                >
                  LinkedIn
                </a>
              )}
              {profile.github && (
                <a
                  href={profile.github}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-gray-900"
                >
                  GitHub
                </a>
              )}
              {profile.website && (
                <a
                  href={profile.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 hover:text-indigo-600"
                >
                  Website
                </a>
              )}
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Hero Section */}
          <div className="bg-white shadow rounded-lg mb-8">
            <div className="px-6 py-8">
              <div className="flex items-center space-x-6">
                {profile.profileImage && (
                  <img
                    src={profile.profileImage}
                    alt={profile.name}
                    className="w-24 h-24 rounded-full object-cover"
                  />
                )}
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{profile.title}</h2>
                  {profile.location && (
                    <p className="text-gray-600 mt-1">{profile.location}</p>
                  )}
                  <p className="text-gray-700 mt-4 max-w-3xl">{profile.bio}</p>
                  
                  <div className="mt-4 flex space-x-4">
                    <a
                      href={`mailto:${profile.email}`}
                      className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                    >
                      Contact Me
                    </a>
                    {profile.resumeUrl && (
                      <a
                        href={profile.resumeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium"
                      >
                        Download Resume
                      </a>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Skills Section */}
          {skills.length > 0 && (
            <div className="bg-white shadow rounded-lg mb-8">
              <div className="px-6 py-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Skills</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries(
                    skills.reduce((acc: any, skill: any) => {
                      if (!acc[skill.category]) {
                        acc[skill.category] = [];
                      }
                      acc[skill.category].push(skill);
                      return acc;
                    }, {})
                  ).map(([category, categorySkills]: [string, any]) => (
                    <div key={category}>
                      <h4 className="font-medium text-gray-900 mb-3">{category}</h4>
                      <div className="space-y-2">
                        {categorySkills.map((skill: any) => (
                          <div key={skill._id} className="flex items-center justify-between">
                            <span className="text-sm text-gray-700">{skill.name}</span>
                            <div className="flex space-x-1">
                              {[1, 2, 3, 4, 5].map((level) => (
                                <div
                                  key={level}
                                  className={`w-2 h-2 rounded-full ${
                                    level <= skill.level ? 'bg-indigo-600' : 'bg-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Projects Section */}
          {projects.length > 0 && (
            <div className="bg-white shadow rounded-lg mb-8">
              <div className="px-6 py-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Projects</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map((project: any) => (
                    <div key={project._id} className="border border-gray-200 rounded-lg overflow-hidden">
                      {project.imageUrl && (
                        <img
                          src={project.imageUrl}
                          alt={project.title}
                          className="w-full h-48 object-cover"
                        />
                      )}
                      <div className="p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-gray-900">{project.title}</h4>
                          {project.featured && (
                            <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                              Featured
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm mb-3">{project.description}</p>
                        
                        <div className="flex flex-wrap gap-1 mb-3">
                          {project.technologies.map((tech: string, index: number) => (
                            <span
                              key={index}
                              className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                        
                        <div className="flex space-x-3">
                          {project.projectUrl && (
                            <a
                              href={project.projectUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                            >
                              Live Demo
                            </a>
                          )}
                          {project.githubUrl && (
                            <a
                              href={project.githubUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-gray-600 hover:text-gray-800 text-sm font-medium"
                            >
                              GitHub
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Experience Section */}
          {experiences.length > 0 && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-8">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Experience</h3>
                <div className="space-y-6">
                  {experiences.map((exp: any) => (
                    <div key={exp._id} className="border-l-4 border-indigo-600 pl-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-gray-900">{exp.position}</h4>
                          <p className="text-indigo-600 font-medium">{exp.company}</p>
                          {exp.location && (
                            <p className="text-gray-600 text-sm">{exp.location}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">
                            {new Date(exp.startDate).toLocaleDateString('en-US', {
                              month: 'short',
                              year: 'numeric'
                            })} - {' '}
                            {exp.current 
                              ? 'Present' 
                              : new Date(exp.endDate).toLocaleDateString('en-US', {
                                  month: 'short',
                                  year: 'numeric'
                                })
                            }
                          </p>
                        </div>
                      </div>
                      <p className="text-gray-700 mt-2">{exp.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <div className="text-center text-gray-600">
            <p>&copy; {new Date().getFullYear()} {profile.name}. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
